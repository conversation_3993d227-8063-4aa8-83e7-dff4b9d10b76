import { eventBus } from "../../src/helpers/eventBus.helper";

/**
 * Generic interface for HTTP clients
 */
interface HttpResponse<T> {
  data: T;
  status?: number;
  headers?: any;
}

interface HttpClient {
  interceptors: {
    request: any;
    response: {
      use: (
        onFulfilled?: (response: any) => any,
        onRejected?: (error: any) => any
      ) => void;
    };
  };
  get: (url: string, options?: any) => Promise<HttpResponse<any>>;
  post: (url: string, data?: any, options?: any) => Promise<HttpResponse<any>>;
  put: (url: string, data?: any, options?: any) => Promise<HttpResponse<any>>;
  patch: (url: string, data?: any, options?: any) => Promise<HttpResponse<any>>;
  delete: (url: string, options?: any) => Promise<HttpResponse<any>>;
}

/**
 * RootAPI class to abstract and adapt the use of fetch libraries.
 */
class RootAPI {
  #clientFetcher: HttpClient;

  /**
   * Create an instance of RootAPI.
   * @param {HttpClient} fetcher - The fetch client instance (e.g., axios, fetch, etc.).
   */
  constructor(fetcher: HttpClient) {
    this.#clientFetcher = fetcher;
    this.enableInterceptors();
  }

  /**
   * Enable interceptors for the fetch client.
   * Interceptors include handling error responses.
   */
  private enableInterceptors(): void {
    this.#clientFetcher.interceptors.response.use(
      (response) => {
        console.log("[RootAPI] response intercepted", response);
        return Promise.resolve(response);
      },
      (error) => {
        console.log("[RootAPI] error intercepted", error);
        console.log("[RootAPI] error.response", error.response);
        console.log("[RootAPI] error.response?.status", error.response?.status);
        console.log("[RootAPI] error.code", error.code);

        // Check if it's a response error with status 401
        if (error.response && error.response.status === 401) {
          console.log("[RootAPI] 401 Unauthorized detected - emitting UNAUTHORIZED_ERROR");
          eventBus.emit("UNAUTHORIZED_ERROR");
        }
        // Check for network errors that might be authentication-related
        else if (error.code === "ERR_NETWORK" && error.config?.url?.includes("/get-one-report/")) {
          console.log("[RootAPI] Network error on authenticated endpoint - likely auth issue - emitting UNAUTHORIZED_ERROR");
          eventBus.emit("UNAUTHORIZED_ERROR");
        }
        // Check for other network errors on authenticated endpoints
        else if (error.code === "ERR_NETWORK" && error.config?.withCredentials) {
          console.log("[RootAPI] Network error on authenticated request - treating as auth issue - emitting UNAUTHORIZED_ERROR");
          eventBus.emit("UNAUTHORIZED_ERROR");
        }
        else {
          console.log("[RootAPI] Not treating as auth error - error.code:", error.code, "withCredentials:", error.config?.withCredentials);
        }

        // Always reject the promise for any error
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, options = {}): Promise<HttpResponse<T>> {
    return this.#clientFetcher.get(url, options);
  }

  async post<T>(url: string, data: any, options = {}): Promise<HttpResponse<T>> {
    return this.#clientFetcher.post(url, data, options);
  }

  async put<T>(url: string, data: any, options = {}): Promise<HttpResponse<T>> {
    return this.#clientFetcher.put(url, data, options);
  }

  async patch<T>(url: string, data: any, options = {}): Promise<HttpResponse<T>> {
    return this.#clientFetcher.patch(url, data, options);
  }

  async delete<T>(url: string, options = {}): Promise<HttpResponse<T>> {
    return this.#clientFetcher.delete(url, options);
  }
}

export default RootAPI;
