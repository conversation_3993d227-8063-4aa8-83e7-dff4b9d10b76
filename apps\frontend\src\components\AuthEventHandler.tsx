import { useEffect } from "react";
import { useAuth } from "~/hooks/useAuth";
import { eventBus } from "~/helpers/eventBus.helper";

export const AuthEventHandler = () => {
  const { logoutMutation } = useAuth();

  useEffect(() => {
    console.log("[AuthEventHandler] Setting up UNAUTHORIZED_ERROR listener");
    const unsubscribe = eventBus.subscribe("UNAUTHORIZED_ERROR", async () => {
      console.log("[AuthEventHandler] UNAUTHORIZED_ERROR event received - triggering logout");
      try {
        await logoutMutation.mutateAsync();
        console.log("[AuthEventHandler] Logout mutation completed successfully");
      } catch (error) {
        console.error("[AuthEventHandler] Logout mutation failed:", error);
      }
    });

    return () => {
      console.log("[AuthEventHandler] Cleaning up UNAUTHORIZED_ERROR listener");
      unsubscribe();
    };
  }, [logoutMutation]);

  return null;
};
